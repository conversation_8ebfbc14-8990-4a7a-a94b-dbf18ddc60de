from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional
from decimal import Decimal

@dataclass
class InvoiceItem:
    product_detail: str
    quantity: Decimal
    price: Decimal
    amount: Decimal
    vat: Optional[Decimal] = None
    gst: Optional[Decimal] = None
    currency: str = "AED"
    original_amount: Optional[Decimal] = None  # For USD original value
    original_currency: Optional[str] = None

@dataclass
class InvoiceData:
    date: datetime
    vendor_name: str
    items: List[InvoiceItem]
    total_amount: Decimal
    currency: str = "AED"
    invoice_number: Optional[str] = None
    tax_amount: Optional[Decimal] = None
    vat: Optional[Decimal] = None
    gst: Optional[Decimal] = None
    original_total_amount: Optional[Decimal] = None
    original_currency: Optional[str] = None
    po_number: Optional[str] = None
    confidence_score: Optional[int] = None

    def validate(self) -> List[str]:
        errors = []
        if not self.vendor_name:
            errors.append("Vendor name is missing")
        if not self.items:
            errors.append("No items found")
        for item in self.items:
            if item.quantity <= 0:
                errors.append(f"Invalid quantity for {item.product_detail}")
            if item.price <= 0:
                errors.append(f"Invalid price for {item.product_detail}")
        return errors 