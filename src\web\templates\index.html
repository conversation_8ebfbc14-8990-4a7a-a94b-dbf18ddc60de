<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>AI Invoice Processing Dashboard</title>
    <script>
    async function processAll() {
        const btn = document.getElementById('process-btn');
        btn.disabled = true;
        btn.innerText = 'Processing...';
        const res = await fetch('/process-all', {method: 'POST'});
        const data = await res.json();
        let msg = '';
        for (const r of data.results) {
            msg += `${r.file}: ${r.status}`;
            if (r.errors && r.errors.length) msg += ` (Validation: ${r.errors.join(', ')})`;
            if (r.error) msg += ` (Error: ${r.error})`;
            msg += '\n';
        }
        alert(msg || 'No invoices processed.');
        btn.disabled = false;
        btn.innerText = 'Process All Pending Invoices';
        window.location.reload();
    }
    </script>
</head>
<body>
    <h1>Welcome to the AI Invoice Processing Web Dashboard!</h1>
    <form action="/upload" method="post" enctype="multipart/form-data">
        <input type="file" name="file" required />
        <button type="submit">Upload Invoice</button>
    </form>
    <button id="process-btn" onclick="processAll()">Process All Pending Invoices</button>
    <h2>Pending Invoices</h2>
    <ul>
        {% for invoice in pending %}
            <li>{{ invoice.name }}</li>
        {% else %}
            <li>No pending invoices.</li>
        {% endfor %}
    </ul>
    <h2>Processed Invoices</h2>
    <ul>
        {% for invoice in processed %}
            {% set inv_id = invoice.invoice_number or (invoice.vendor_name ~ '_' ~ (invoice.date.strftime('%Y-%m-%d') if invoice.date else '')) %}
            <li>
                <b>{{ invoice.vendor_name }}</b> ({{ invoice.date.strftime('%Y-%m-%d') if invoice.date else '' }})
                [<a href="/invoice/{{ inv_id }}" target="_blank">View</a>]
                [<a href="/export-invoice/{{ inv_id }}/excel">Excel</a>]
                [<a href="/export-invoice/{{ inv_id }}/csv">CSV</a>]
                [<a href="/export-invoice/{{ inv_id }}/json">JSON</a>]
            </li>
        {% else %}
            <li>No processed invoices.</li>
        {% endfor %}
    </ul>
    <h2>Export All</h2>
    <a href="/export/excel">Download Excel</a> |
    <a href="/export/csv">Download CSV</a> |
    <a href="/export/json">Download JSON</a>
</body>
</html> 