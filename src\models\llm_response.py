from dataclasses import dataclass
from typing import List, Optional
from decimal import Decimal
from datetime import datetime

@dataclass
class LLMInvoiceItem:
    product_detail: str
    quantity: Decimal
    price: Decimal
    amount: Decimal

@dataclass
class LLMInvoiceResponse:
    date: str
    vendor_name: str
    invoice_number: Optional[str]
    currency: str
    items: List[LLMInvoiceItem]
    total_amount: Decimal
    tax_amount: Optional[Decimal]
    confidence_score: int 