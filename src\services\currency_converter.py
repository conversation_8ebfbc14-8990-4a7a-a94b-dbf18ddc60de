import requests
from decimal import Decimal
from typing import Optional

class CurrencyConverter:
    def __init__(self, api_url: str):
        self.api_url = api_url
        self._usd_to_aed = None

    def get_usd_to_aed(self) -> Optional[Decimal]:
        if self._usd_to_aed is not None:
            return self._usd_to_aed
        resp = requests.get(self.api_url)
        if resp.status_code == 200:
            data = resp.json()
            rate = data.get('rates', {}).get('AED')
            if rate:
                self._usd_to_aed = Decimal(str(rate))
                return self._usd_to_aed
        return None

    def convert_usd_to_aed(self, amount: Decimal) -> Optional[Decimal]:
        rate = self.get_usd_to_aed()
        if rate:
            return amount * rate
        return None 