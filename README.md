# AI Invoice Processing Application

## Overview
A comprehensive Python application with a FastAPI-powered web dashboard for processing invoice images (PDF, PNG, JPG, TIFF) using AI, extracting data, and generating organized Excel, CSV, and JSON reports. Supports multi-currency (AED, USD with auto-conversion), multi-tax, vendor management, and automatic local backups.

## Features
- AI-powered invoice data extraction (English & Arabic)
- Multi-LLM support (DeepSeek, OpenAI, Claude, Gemini, Mistral, OpenRouter, Custom)
- Excel, CSV, and JSON report generation (month/year organization)
- Automatic USD→AED conversion (live rates)
- Separate columns for VAT, GST, etc.
- Vendor database for consistency
- Web dashboard for upload, management, and export
- Automatic backup to a local folder
- Robust error handling, logging, and monitoring

## Project Structure
```
invoice-processor/
├── src/                  # Backend and web dashboard
├── data/
│   ├── invoices/         # Input folder
│   └── processed/        # Archive folder
├── reports/              # Generated reports
├── backup/               # Automatic backups
├── logs/                 # Application logs
├── requirements.txt
├── .env.example
├── README.md
└── setup.py
```

## Setup Instructions
1. Clone the repository
2. Run the setup script: `bash setup.sh` (or follow manual steps)
3. Place invoice images in `./data/invoices/`
4. Configure API keys in `.env`
5. Start the web dashboard: `uvicorn src.main:app --reload`

## Usage
- Access the web dashboard at `http://localhost:8000`
- Upload invoices, view reports, export data, and manage vendors

## Advanced
- CLI commands available for batch processing and status
- All settings configurable via `.env`

## Requirements
See `requirements.txt` for dependencies.

---
For more details, see the full documentation in the repo. 