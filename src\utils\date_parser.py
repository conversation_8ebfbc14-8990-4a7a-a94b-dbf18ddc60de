from datetime import datetime
from typing import Optional
import re

class DateParser:
    FORMATS = [
        "%d/%m/%Y", "%d-%m-%Y", "%d/%m/%y", "%d-%b-%Y", "%Y-%m-%d", "%d.%m.%Y",
        "%m/%d/%Y", "%m-%d-%Y", "%d %B %Y", "%d %b %Y"
    ]

    @staticmethod
    def parse(date_str: str) -> Optional[datetime]:
        for fmt in DateParser.FORMATS:
            try:
                return datetime.strptime(date_str, fmt)
            except Exception:
                continue
        # Try to extract date using regex as fallback
        match = re.search(r"\d{4}-\d{2}-\d{2}", date_str)
        if match:
            try:
                return datetime.strptime(match.group(), "%Y-%m-%d")
            except Exception:
                pass
        return None 