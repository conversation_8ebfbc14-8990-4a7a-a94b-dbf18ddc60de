import pytesseract
from PIL import Image
from pdf2image import convert_from_path
import os
from typing import Union
from pathlib import Path
import asyncio
import platform

class ImageProcessor:
    def __init__(self, ocr_languages: str = "eng+ara"):
        self.ocr_languages = ocr_languages
        # Configure Tesseract path for Windows
        if platform.system() == "Windows":
            tesseract_path = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
            if os.path.exists(tesseract_path):
                pytesseract.pytesseract.tesseract_cmd = tesseract_path

    async def extract_text(self, file_path: Union[str, Path]) -> str:
        ext = str(file_path).lower()
        if ext.endswith('.pdf'):
            return await self._extract_text_from_pdf(file_path)
        else:
            return await self._extract_text_from_image(file_path)

    async def _extract_text_from_image(self, image_path: Union[str, Path]) -> str:
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self._ocr_image, image_path)

    def _ocr_image(self, image_path: Union[str, Path]) -> str:
        image = Image.open(image_path)
        return pytesseract.image_to_string(image, lang=self.ocr_languages)

    async def _extract_text_from_pdf(self, pdf_path: Union[str, Path]) -> str:
        loop = asyncio.get_event_loop()
        images = await loop.run_in_executor(None, convert_from_path, pdf_path)
        text = ""
        for img in images:
            text += pytesseract.image_to_string(img, lang=self.ocr_languages)
        return text 