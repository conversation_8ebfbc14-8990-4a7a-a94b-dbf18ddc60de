import json
from pathlib import Path
from src.models.vendor import Vendor
from typing import List, Optional

class VendorManager:
    def __init__(self, db_path: str):
        self.db_path = Path(db_path)
        self.vendors = self._load_vendors()

    def _load_vendors(self) -> List[Vendor]:
        if self.db_path.exists():
            with open(self.db_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return [Vendor(**v) for v in data]
        return []

    def save(self):
        with open(self.db_path, 'w', encoding='utf-8') as f:
            json.dump([v.__dict__ for v in self.vendors], f, ensure_ascii=False, indent=2)

    def find_vendor(self, name: str) -> Optional[Vendor]:
        for v in self.vendors:
            if v.name.lower() == name.lower():
                return v
        return None

    def add_or_update_vendor(self, vendor: Vendor):
        existing = self.find_vendor(vendor.name)
        if existing:
            self.vendors.remove(existing)
        self.vendors.append(vendor)
        self.save() 