AI Invoice Processing Application - Complete Implementation Prompt

Project Overview
Create a comprehensive Python application that uses AI to process invoice images and generate organized Excel reports. The application should be production-ready with robust error handling, multi-language support, and extensible LLM integration.

Application Architecture

Core Requirements
1. Multi-LLM Support with DeepSeek as default
2. Invoice Processing Pipeline with AI extraction
3. Excel Report Generation organized by month/year
4. File Management with processed invoice archiving
5. Multi-language invoice recognition
6. Comprehensive logging and error handling

Project Structure
```
invoice-processor/
├── src/
│   ├── __init__.py
│   ├── main.py                 Entry point
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py         Configuration management
│   │   └── llm_providers.py    LLM provider definitions
│   ├── models/
│   │   ├── __init__.py
│   │   ├── invoice_data.py     Data models
│   │   └── llm_response.py     LLM response models
│   ├── services/
│   │   ├── __init__.py
│   │   ├── image_processor.py  OCR and image handling
│   │   ├── llm_service.py      LLM interaction layer
│   │   ├── excel_generator.py  Excel file management
│   │   └── file_manager.py     File operations
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── date_parser.py      Multi-format date parsing
│   │   ├── validators.py       Data validation
│   │   └── logger.py           Logging configuration
│   └── tests/
│       ├── __init__.py
│       ├── test_image_processor.py
│       ├── test_llm_service.py
│       └── test_excel_generator.py
├── data/
│   ├── invoices/               Input folder
│   └── processed/              Archive folder
├── reports/                    Generated Excel files
├── logs/                       Application logs
├── requirements.txt
├── .env.example
├── README.md
└── setup.py
```

Detailed Implementation Requirements

1. Configuration Management
```python
.env.example
LLM Configuration
DEFAULT_LLM_PROVIDER=deepseek
DEEPSEEK_API_KEY=your_deepseek_key
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_claude_key
GOOGLE_API_KEY=your_gemini_key
OPENROUTER_API_KEY=your_openrouter_key
MISTRAL_API_KEY=your_mistral_key

Custom LLM Configuration
CUSTOM_LLM_BASE_URL=https://your-custom-llm.com/v1
CUSTOM_LLM_API_KEY=your_custom_key
CUSTOM_LLM_MODEL_NAME=your-model-name

Application Settings
INVOICE_FOLDER=./data/invoices
PROCESSED_FOLDER=./data/processed
REPORTS_FOLDER=./reports
LOG_LEVEL=INFO
MAX_CONCURRENT_REQUESTS=5
RETRY_ATTEMPTS=3
```

2. LLM Integration Layer
```python
src/config/llm_providers.py
from enum import Enum
from typing import Dict, Any

class LLMProvider(Enum):
    DEEPSEEK = "deepseek"
    OPENAI = "openai"
    CLAUDE = "claude"
    GEMINI = "gemini"
    MISTRAL = "mistral"
    OPENROUTER = "openrouter"
    CUSTOM = "custom"

class LLMConfig:
    def __init__(self, provider: LLMProvider, api_key: str, base_url: str = None, model: str = None):
        self.provider = provider
        self.api_key = api_key
        self.base_url = base_url
        self.model = model or self.get_default_model()
    
    def get_default_model(self) -> str:
        defaults = {
            LLMProvider.DEEPSEEK: "deepseek-chat",
            LLMProvider.OPENAI: "gpt-4-turbo-preview",
            LLMProvider.CLAUDE: "claude-3-sonnet-20240229",
            LLMProvider.GEMINI: "gemini-pro",
            LLMProvider.MISTRAL: "mistral-large-latest",
            LLMProvider.OPENROUTER: "openai/gpt-4-turbo-preview"
        }
        return defaults.get(self.provider, "default-model")
```

3. Invoice Data Model
```python
src/models/invoice_data.py
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional
from decimal import Decimal

@dataclass
class InvoiceItem:
    product_detail: str
    quantity: Decimal
    price: Decimal
    amount: Decimal

@dataclass
class InvoiceData:
    date: datetime
    vendor_name: str
    items: List[InvoiceItem]
    total_amount: Decimal
    currency: str = "USD"
    invoice_number: Optional[str] = None
    tax_amount: Optional[Decimal] = None
    
    def validate(self) -> List[str]:
        """Validate invoice data and return list of errors"""
        errors = []
        if not self.vendor_name:
            errors.append("Vendor name is missing")
        if not self.items:
            errors.append("No items found")
        for item in self.items:
            if item.quantity <= 0:
                errors.append(f"Invalid quantity for {item.product_detail}")
            if item.price <= 0:
                errors.append(f"Invalid price for {item.product_detail}")
        return errors
```

4. AI Processing Prompt
```python
src/services/llm_service.py
INVOICE_EXTRACTION_PROMPT = """
You are an expert invoice data extractor. Analyze the provided invoice image and extract all relevant information with high accuracy.

Instructions:
1. Extract ALL text from the invoice image
2. Identify the invoice date (handle multiple date formats: DD/MM/YY, DD-MMM-YYYY, etc.)
3. Extract vendor/company name
4. For each line item, extract:
   - Product/service description
   - Quantity (convert to decimal number)
   - Unit price (convert to decimal number)
   - Total amount for the line (convert to decimal number)
5. Calculate the total invoice amount
6. Identify the currency (USD, EUR, etc.)
7. Extract invoice number if available
8. Extract tax amount if specified

Output Format (JSON):
{
    "date": "YYYY-MM-DD",
    "vendor_name": "string",
    "invoice_number": "string or null",
    "currency": "string",
    "items": [
        {
            "product_detail": "string",
            "quantity": decimal,
            "price": decimal,
            "amount": decimal
        }
    ],
    "total_amount": decimal,
    "tax_amount": decimal or null,
    "confidence_score": 0-100
}

Language Support:
- Support invoices in English, Spanish, French, German, Italian, Portuguese, Chinese, Japanese, Korean, Arabic, and Hindi
- Maintain original language for product descriptions

Error Handling:
- If any field is unclear, use null
- If date format is ambiguous, provide your best interpretation with confidence score
- If amounts are unclear, extract what you can and flag with low confidence
"""
```

5. Main Application Flow
```python
src/main.py
import asyncio
import sys
from pathlib import Path
from typing import List
import typer
from rich.console import Console
from rich.progress import Progress, TaskID
from rich.table import Table
from rich.prompt import Prompt, Confirm

from src.services.image_processor import ImageProcessor
from src.services.llm_service import LLMService
from src.services.excel_generator import ExcelGenerator
from src.services.file_manager import FileManager
from src.config.settings import Settings
from src.utils.logger import get_logger

app = typer.Typer()
console = Console()
logger = get_logger(__name__)

class InvoiceProcessor:
    def __init__(self):
        self.settings = Settings()
        self.image_processor = ImageProcessor()
        self.llm_service = LLMService(self.settings.default_llm)
        self.excel_generator = ExcelGenerator(self.settings.reports_folder)
        self.file_manager = FileManager(
            self.settings.invoice_folder,
            self.settings.processed_folder
        )
    
    async def process_single_invoice(self, invoice_path: Path, progress: Progress, task_id: TaskID) -> bool:
        """Process a single invoice with full error handling"""
        try:
            Step 1: Extract text from image
            progress.update(task_id, description=f"Processing {invoice_path.name} - OCR")
            extracted_text = await self.image_processor.extract_text(invoice_path)
            
            Step 2: AI processing
            progress.update(task_id, description=f"Processing {invoice_path.name} - AI Analysis")
            invoice_data = await self.llm_service.extract_invoice_data(extracted_text, invoice_path)
            
            Step 3: Validate data
            errors = invoice_data.validate()
            if errors:
                logger.warning(f"Validation errors for {invoice_path.name}: {errors}")
                if not Confirm.ask(f"Continue with {invoice_path.name} despite validation errors?"):
                    return False
            
            Step 4: Save to Excel
            progress.update(task_id, description=f"Processing {invoice_path.name} - Saving")
            await self.excel_generator.add_invoice(invoice_data)
            
            Step 5: Move to processed
            self.file_manager.move_to_processed(invoice_path)
            
            progress.update(task_id, completed=100)
            return True
            
        except Exception as e:
            logger.error(f"Failed to process {invoice_path.name}: {str(e)}")
            progress.update(task_id, description=f"Error: {invoice_path.name}")
            return False
    
    async def process_all_invoices(self) -> None:
        """Process all pending invoices"""
        pending_invoices = self.file_manager.get_pending_invoices()
        
        if not pending_invoices:
            console.print("[yellow]No pending invoices found[/yellow]")
            return
        
        console.print(f"[green]Found {len(pending_invoices)} pending invoices[/green]")
        
        with Progress() as progress:
            task = progress.add_task("Processing invoices...", total=len(pending_invoices))
            
            Process invoices with concurrency limit
            semaphore = asyncio.Semaphore(self.settings.max_concurrent_requests)
            
            async def process_with_semaphore(invoice_path: Path):
                async with semaphore:
                    async with asyncio.timeout(self.settings.request_timeout):
                        return await self.process_single_invoice(invoice_path, progress, task)
            
            results = await asyncio.gather(
                *[process_with_semaphore(inv) for inv in pending_invoices],
                return_exceptions=True
            )
        
        success_count = sum(1 for r in results if r is True)
        console.print(f"[green]Successfully processed {success_count}/{len(pending_invoices)} invoices[/green]")

@app.command()
def process(
    provider: str = typer.Option(None, help="LLM provider to use"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Verbose logging")
):
    """Process all pending invoices"""
    if verbose:
        logger.setLevel("DEBUG")
    
    processor = InvoiceProcessor()
    
    if provider:
        processor.llm_service.switch_provider(provider)
    
    asyncio.run(processor.process_all_invoices())

@app.command()
def config():
    """Interactive configuration setup"""
    console.print("[bold blue]Invoice Processor Configuration[/bold blue]")
    
    Provider selection
    providers = ["deepseek", "openai", "claude", "gemini", "mistral", "openrouter", "custom"]
    selected = Prompt.ask(
        "Select LLM provider",
        choices=providers,
        default="deepseek"
    )
    
    api_key = Prompt.ask(f"Enter {selected} API key", password=True)
    
    Save configuration
    config_path = Path(".env")
    if config_path.exists():
        if not Confirm.ask("Overwrite existing configuration?"):
            return
    
    with open(config_path, "w") as f:
        f.write(f"DEFAULT_LLM_PROVIDER={selected}\n")
        f.write(f"{selected.upper()}_API_KEY={api_key}\n")
    
    console.print("[green]Configuration saved![/green]")

@app.command()
def status():
    """Show current status and statistics"""
    processor = InvoiceProcessor()
    
    pending = len(processor.file_manager.get_pending_invoices())
    processed = len(list(Path(processor.settings.processed_folder).glob("*")))
    
    table = Table(title="Invoice Processor Status")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="magenta")
    
    table.add_row("Pending Invoices", str(pending))
    table.add_row("Processed Invoices", str(processed))
    table.add_row("Current LLM", processor.llm_service.current_provider)
    table.add_row("Reports Location", str(processor.settings.reports_folder))
    
    console.print(table)

if __name__ == "__main__":
    app()
```

6. Installation and Setup Script
```bash
!/bin/bash
setup.sh

echo "🚀 Setting up Invoice Processor..."

Create directories
mkdir -p data/invoices data/processed reports logs

Create virtual environment
python -m venv venv
source venv/bin/activate  On Windows: venv\Scripts\activate

Install dependencies
pip install -r requirements.txt

Create initial configuration
cp .env.example .env

echo "✅ Setup complete!"
echo "📁 Place your invoice images in ./data/invoices/"
echo "🔑 Configure your API keys in .env file"
echo "🎉 Run: python -m src.main process"
```

7. Requirements File
```txt
requirements.txt
typer[all]==0.9.0
openai==1.30.1
anthropic==0.28.0
google-generativeai==0.5.4
mistralai==0.1.8
httpx==0.27.0
pillow==10.3.0
opencv-python==********
pytesseract==0.3.10
pandas==2.2.2
openpyxl==3.1.2
python-dotenv==1.0.1
rich==13.7.1
aiofiles==23.2.1
asyncio-throttle==1.0.2
tenacity==8.3.0
pydantic==2.7.1
deepseek-py==1.0.0
```

Usage Instructions

Initial Setup
1. Run `bash setup.sh` or manually create directories
2. Copy invoice images to `./data/invoices/`
3. Configure API keys in `.env` file
4. Run configuration wizard: `python -m src.main config`

Daily Usage
```bash
Process all pending invoices
python -m src.main process

Process with specific provider
python -m src.main process --provider openai

Check status
python -m src.main status

Verbose processing
python -m src.main process --verbose
```

Advanced Features

1. Batch Processing with Progress
- Real-time progress bars
- Concurrent processing with rate limiting
- Detailed error reporting

2. Data Validation
- Automatic date format detection
- Currency validation
- Amount verification
- Duplicate detection

3. Error Recovery
- Retry mechanism for failed API calls
- Failed invoices moved to separate folder
- Detailed logging for debugging

4. Excel Features
- Automatic month-wise sheets
- Currency formatting
- Totals and subtotals
- Conditional formatting for errors
- Data validation rules

5. Monitoring
- Processing statistics
- API usage tracking
- Error rate monitoring
- Performance metrics

Follow-up Questions

1. Invoice Formats: Do you need support for specific invoice formats (PDF, PNG, JPG, TIFF)?
2. Currency Handling: Should the system handle multiple currencies in the same report?
3. Tax Details: Do you need separate columns for different tax types (VAT, GST, etc.)?
4. Vendor Management: Should we maintain a vendor database for consistency?
5. Email Integration: Do you want email notifications for processing completion?
6. Web Interface: Would you prefer a web dashboard instead of CLI?
7. Data Export: Need CSV or JSON export options?
8. OCR Language: Any specific OCR language packs needed?
9. Custom Fields: Any additional fields to extract (PO numbers, payment terms)?
10. Backup Strategy: How should we handle backup of processed invoices and reports?