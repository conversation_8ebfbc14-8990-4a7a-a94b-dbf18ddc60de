from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, Request, Form, BackgroundTasks
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
import typer
from pathlib import Path
import shutil
import os
from src.config.settings import Settings
from src.services.image_processor import ImageProcessor
from src.services.llm_service import LLMService
from src.services.excel_generator import ExcelGenerator
from src.services.file_manager import FileManager
from src.services.currency_converter import CurrencyConverter
from src.services.vendor_manager import VendorManager
from src.utils.logger import get_logger
from src.models.invoice_data import InvoiceData
import pandas as pd
import json
from fastapi import Path as FastAPIPath

app = FastAPI(title="AI Invoice Processing Application")
cli = typer.Typer()

# Web dashboard setup
BASE_DIR = Path(__file__).parent
TEMPLATES_DIR = BASE_DIR / "web" / "templates"
STATIC_DIR = BASE_DIR / "web" / "static"
templates = Jinja2Templates(directory=str(TEMPLATES_DIR))
app.mount("/static", StaticFiles(directory=str(STATIC_DIR)), name="static")

# CORS for web dashboard
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Service singletons
settings = Settings()
logger = get_logger(__name__)
image_processor = ImageProcessor(settings.ocr_languages)
llm_service = LLMService(None)  # Will be set up with config in real use
excel_generator = ExcelGenerator(settings.reports_folder)
file_manager = FileManager(settings.invoice_folder, settings.processed_folder, settings.backup_folder)
currency_converter = CurrencyConverter(settings.currency_api_url)
vendor_manager = VendorManager(settings.vendor_db_path)

@app.post("/process-all")
async def process_all_invoices(background_tasks: BackgroundTasks):
    pending = file_manager.get_pending_invoices()
    results = []
    for invoice_path in pending:
        try:
            # Step 1: OCR
            extracted_text = await image_processor.extract_text(invoice_path)
            # Step 2: LLM extraction (dummy for now)
            invoice_data = await llm_service.extract_invoice_data(extracted_text, str(invoice_path))
            # Step 3: Validation
            errors = invoice_data.validate()
            # Step 4: Currency conversion if needed
            if invoice_data.currency == "USD":
                rate = currency_converter.get_usd_to_aed()
                if rate:
                    invoice_data.original_total_amount = invoice_data.total_amount
                    invoice_data.original_currency = "USD"
                    invoice_data.total_amount = currency_converter.convert_usd_to_aed(invoice_data.total_amount)
                    invoice_data.currency = "AED"
            # Step 5: Add to report
            await excel_generator.add_invoice(invoice_data)
            # Step 6: Move to processed and backup
            file_manager.move_to_processed(invoice_path)
            results.append({"file": invoice_path.name, "status": "success", "errors": errors})
        except Exception as e:
            logger.error(f"Failed to process {invoice_path.name}: {str(e)}")
            results.append({"file": invoice_path.name, "status": "error", "error": str(e)})
    return {"results": results}

@app.get("/processed-invoices")
def list_processed_invoices():
    invoices = getattr(excel_generator, 'processed_invoices', [])
    return {"invoices": [i.invoice_number or f"{i.vendor_name}_{i.date.strftime('%Y-%m-%d') if i.date else ''}" for i in invoices]}

@app.get("/invoice/{invoice_id}")
def get_invoice(invoice_id: str):
    invoices = getattr(excel_generator, 'processed_invoices', [])
    for inv in invoices:
        inv_id = inv.invoice_number or f"{inv.vendor_name}_{inv.date.strftime('%Y-%m-%d') if inv.date else ''}"
        if inv_id == invoice_id:
            return excel_generator._invoice_to_dict(inv)
    return JSONResponse({"error": "Invoice not found"}, status_code=404)

@app.get("/export-invoice/{invoice_id}/{format}")
def export_single_invoice(invoice_id: str, format: str):
    invoices = getattr(excel_generator, 'processed_invoices', [])
    for inv in invoices:
        inv_id = inv.invoice_number or f"{inv.vendor_name}_{inv.date.strftime('%Y-%m-%d') if inv.date else ''}"
        if inv_id == invoice_id:
            if format == "excel":
                path = Path(settings.reports_folder) / f"invoice_{invoice_id}.xlsx"
                df = excel_generator._invoices_to_df([inv])
                with pd.ExcelWriter(path) as writer:
                    df.to_excel(writer, index=False)
                return FileResponse(path, filename=f"invoice_{invoice_id}.xlsx")
            elif format == "csv":
                path = excel_generator.export_csv([inv], f"invoice_{invoice_id}.csv")
                return FileResponse(path, filename=f"invoice_{invoice_id}.csv")
            elif format == "json":
                path = excel_generator.export_json([inv], f"invoice_{invoice_id}.json")
                return FileResponse(path, filename=f"invoice_{invoice_id}.json")
            else:
                return JSONResponse({"error": "Invalid format"}, status_code=400)
    return JSONResponse({"error": "Invoice not found"}, status_code=404)

@app.get("/", response_class=HTMLResponse)
def dashboard(request: Request):
    pending = file_manager.get_pending_invoices()
    processed = getattr(excel_generator, 'processed_invoices', [])
    return templates.TemplateResponse("index.html", {"request": request, "pending": pending, "processed": processed})

@app.post("/upload")
async def upload_invoice(file: UploadFile = File(...)):
    # Save uploaded file to invoice folder
    dest = Path(settings.invoice_folder) / file.filename
    with open(dest, "wb") as f:
        shutil.copyfileobj(file.file, f)
    return {"filename": file.filename, "status": "uploaded"}

@app.get("/invoices")
def list_invoices():
    # List all pending invoices
    pending = file_manager.get_pending_invoices()
    return {"invoices": [str(p) for p in pending]}

def get_all_processed_invoices():
    processed_dir = Path(settings.processed_folder)
    invoices = []
    for file in processed_dir.iterdir():
        # For demo, skip actual parsing; in real use, load from a DB or cache
        # Here, just return empty list or could parse from Excel/JSON if stored
        pass
    # For now, use in-memory processed_invoices if available
    return getattr(excel_generator, 'processed_invoices', [])

@app.get("/export/{format}")
def export_invoices(format: str):
    invoices = get_all_processed_invoices()
    if not invoices:
        return JSONResponse({"error": "No processed invoices to export."}, status_code=400)
    if format == "excel":
        # Export all to a single Excel file
        path = Path(settings.reports_folder) / "invoices_export.xlsx"
        df = excel_generator._invoices_to_df(invoices)
        with pd.ExcelWriter(path) as writer:
            df.to_excel(writer, index=False)
        return FileResponse(path, filename="invoices_export.xlsx")
    elif format == "csv":
        path = excel_generator.export_csv(invoices, "invoices_export.csv")
        return FileResponse(path, filename="invoices_export.csv")
    elif format == "json":
        path = excel_generator.export_json(invoices, "invoices_export.json")
        return FileResponse(path, filename="invoices_export.json")
    else:
        return JSONResponse({"error": "Invalid format"}, status_code=400)

# CLI placeholder for batch processing, config, etc.
if __name__ == "__main__":
    cli() 