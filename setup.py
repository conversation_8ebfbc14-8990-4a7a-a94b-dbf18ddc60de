from setuptools import setup, find_packages

setup(
    name='invoice-processor',
    version='0.1.0',
    packages=find_packages(where='src'),
    package_dir={'': 'src'},
    install_requires=[
        'typer[all]==0.9.0',
        'openai==1.30.1',
        'anthropic==0.28.0',
        'google-generativeai==0.5.4',
        'mistralai==0.1.8',
        'httpx==0.27.0',
        'pillow==10.3.0',
        'opencv-python==********',
        'pytesseract==0.3.10',
        'pandas==2.2.2',
        'openpyxl==3.1.2',
        'python-dotenv==1.0.1',
        'rich==13.7.1',
        'aiofiles==23.2.1',
        'asyncio-throttle==1.0.2',
        'tenacity==8.3.0',
        'pydantic==2.7.1',
        'deepseek-py==1.0.0',
        'fastapi==0.111.0',
        'uvicorn[standard]==0.29.0',
        'jinja2==3.1.3',
        'requests==2.32.2',
    ],
    entry_points={
        'console_scripts': [
            'invoice-processor=src.main:cli',
        ],
    },
    include_package_data=True,
    description='AI-powered invoice processing with web dashboard',
    author='Naeem Hanfi',
    url='https://github.com/yourusername/invoice-processor',
) 