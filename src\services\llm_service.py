import os
from src.config.llm_providers import LLMProvider, LLMConfig
from src.models.invoice_data import InvoiceData, InvoiceItem
from src.models.llm_response import LLMInvoiceResponse
from typing import Any
import asyncio
import openai
import json
from decimal import Decimal
from datetime import datetime
from src.utils.date_parser import DateParser

INVOICE_EXTRACTION_PROMPT = """
You are an expert invoice data extractor. Analyze the provided invoice image and extract all relevant information with high accuracy.

Instructions:
1. Extract ALL text from the invoice image
2. Identify the invoice date (handle multiple date formats: DD/MM/YY, DD-MMM-YYYY, etc.)
3. Extract vendor/company name
4. For each line item, extract:
   - Product/service description
   - Quantity (convert to decimal number)
   - Unit price (convert to decimal number)
   - Total amount for the line (convert to decimal number)
5. Calculate the total invoice amount
6. Identify the currency (USD, EUR, etc.)
7. Extract invoice number if available
8. Extract tax amount if specified

Output Format (JSON):
{
    "date": "YYYY-MM-DD",
    "vendor_name": "string",
    "invoice_number": "string or null",
    "currency": "string",
    "items": [
        {
            "product_detail": "string",
            "quantity": decimal,
            "price": decimal,
            "amount": decimal
        }
    ],
    "total_amount": decimal,
    "tax_amount": decimal or null,
    "confidence_score": 0-100
}

Language Support:
- Support invoices in English, Spanish, French, German, Italian, Portuguese, Chinese, Japanese, Korean, Arabic, and Hindi
- Maintain original language for product descriptions

Error Handling:
- If any field is unclear, use null
- If date format is ambiguous, provide your best interpretation with confidence score
- If amounts are unclear, extract what you can and flag with low confidence
"""

class LLMService:
    def __init__(self, llm_config: LLMConfig = None):
        self.llm_config = llm_config
        self.current_provider = llm_config.provider if llm_config else LLMProvider.OPENAI
        self.api_key = os.getenv("OPENAI_API_KEY")
        openai.api_key = self.api_key

    async def extract_invoice_data(self, extracted_text: str, invoice_path: str) -> InvoiceData:
        prompt = INVOICE_EXTRACTION_PROMPT + "\n\nInvoice Text:\n" + extracted_text
        try:
            # For now, only OpenAI is implemented
            response = await self._call_openai(prompt)
            return self._parse_llm_response(response)
        except Exception as e:
            # Fallback: return empty invoice with error
            return InvoiceData(
                date=None,
                vendor_name="ERROR: " + str(e),
                items=[],
                total_amount=Decimal(0)
            )

    async def _call_openai(self, prompt: str) -> str:
        loop = asyncio.get_event_loop()
        completion = await loop.run_in_executor(
            None,
            lambda: openai.chat.completions.create(
                model="gpt-4-turbo-preview",
                messages=[{"role": "system", "content": INVOICE_EXTRACTION_PROMPT},
                          {"role": "user", "content": prompt}],
                temperature=0.0,
                max_tokens=1024,
            )
        )
        # Try to extract JSON from the response
        content = completion.choices[0].message.content
        return content

    def _parse_llm_response(self, response: str) -> InvoiceData:
        try:
            # Find JSON in response
            start = response.find('{')
            end = response.rfind('}') + 1
            json_str = response[start:end]
            data = json.loads(json_str)
            # Parse items
            items = [InvoiceItem(
                product_detail=i["product_detail"],
                quantity=Decimal(str(i["quantity"])),
                price=Decimal(str(i["price"])),
                amount=Decimal(str(i["amount"]))
            ) for i in data.get("items", [])]
            # Parse date
            date = DateParser.parse(data.get("date", ""))
            return InvoiceData(
                date=date,
                vendor_name=data.get("vendor_name", ""),
                items=items,
                total_amount=Decimal(str(data.get("total_amount", 0))),
                currency=data.get("currency", "AED"),
                invoice_number=data.get("invoice_number"),
                tax_amount=Decimal(str(data["tax_amount"])) if data.get("tax_amount") else None,
                confidence_score=int(data.get("confidence_score", 0))
            )
        except Exception as e:
            return InvoiceData(
                date=None,
                vendor_name="ERROR: " + str(e),
                items=[],
                total_amount=Decimal(0)
            )

    def switch_provider(self, provider: str):
        self.current_provider = provider 