import os
from dotenv import load_dotenv
from pathlib import Path

load_dotenv()

class Settings:
    def __init__(self):
        self.default_llm = os.getenv("DEFAULT_LLM_PROVIDER", "deepseek")
        self.invoice_folder = os.getenv("INVOICE_FOLDER", "./data/invoices")
        self.processed_folder = os.getenv("PROCESSED_FOLDER", "./data/processed")
        self.reports_folder = os.getenv("REPORTS_FOLDER", "./reports")
        self.backup_folder = os.getenv("BACKUP_FOLDER", "./backup")
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.max_concurrent_requests = int(os.getenv("MAX_CONCURRENT_REQUESTS", 5))
        self.retry_attempts = int(os.getenv("RETRY_ATTEMPTS", 3))
        self.ocr_languages = os.getenv("OCR_LANGUAGES", "eng+ara")
        self.currency_api_url = os.getenv("CURRENCY_API_URL", "https://open.er-api.com/v6/latest/USD")
        self.vendor_db_path = os.getenv("VENDOR_DB_PATH", "./data/vendors.json")
        self.request_timeout = int(os.getenv("REQUEST_TIMEOUT", 60)) 