import pandas as pd
from pathlib import Path
from src.models.invoice_data import InvoiceData
from typing import List
import os
from datetime import datetime
import json
from decimal import Decimal

class ExcelGenerator:
    def __init__(self, reports_folder: str):
        self.reports_folder = Path(reports_folder)
        self.reports_folder.mkdir(parents=True, exist_ok=True)
        self.processed_invoices: List[InvoiceData] = self._load_processed_invoices()

    def _load_processed_invoices(self) -> List[InvoiceData]:
        path = self.reports_folder / 'processed_invoices.json'
        if not path.exists():
            return []
        with open(path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            from src.models.invoice_data import InvoiceItem
            invoices = []
            for inv in data:
                items = [InvoiceItem(**item) for item in inv['items']]
                from src.utils.date_parser import DateParser
                date = DateParser.parse(inv.get('date', ''))
                invoices.append(InvoiceData(
                    date=date,
                    vendor_name=inv.get('vendor_name', ''),
                    items=items,
                    total_amount=Decimal(str(inv.get('total_amount', 0))),
                    currency=inv.get('currency', 'AED'),
                    invoice_number=inv.get('invoice_number'),
                    tax_amount=Decimal(str(inv['tax_amount'])) if inv.get('tax_amount') else None,
                    vat=Decimal(str(inv['vat'])) if inv.get('vat') else None,
                    gst=Decimal(str(inv['gst'])) if inv.get('gst') else None,
                    original_total_amount=Decimal(str(inv['original_total_amount'])) if inv.get('original_total_amount') else None,
                    original_currency=inv.get('original_currency'),
                    po_number=inv.get('po_number'),
                    confidence_score=int(inv.get('confidence_score', 0)) if inv.get('confidence_score') else None
                ))
            return invoices

    def _save_processed_invoices(self):
        path = self.reports_folder / 'processed_invoices.json'
        data = [self._invoice_to_dict(inv) for inv in self.processed_invoices]
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)

    async def add_invoice(self, invoice: InvoiceData):
        # Organize by month/year
        date = invoice.date or datetime.now()
        report_path = self.reports_folder / f"invoices_{date.year}_{date.month:02d}.xlsx"
        self.processed_invoices.append(invoice)
        self._save_processed_invoices()
        # Convert to DataFrame
        df = self._invoices_to_df([invoice])
        # Append or create Excel file
        if report_path.exists():
            existing = pd.read_excel(report_path)
            df = pd.concat([existing, df], ignore_index=True)
        with pd.ExcelWriter(report_path) as writer:
            df.to_excel(writer, index=False)

    def export_csv(self, invoices: List[InvoiceData], filename: str):
        df = self._invoices_to_df(invoices)
        path = self.reports_folder / filename
        df.to_csv(path, index=False)
        return path

    def export_json(self, invoices: List[InvoiceData], filename: str):
        data = [self._invoice_to_dict(inv) for inv in invoices]
        path = self.reports_folder / filename
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)
        return path

    def _invoices_to_df(self, invoices: List[InvoiceData]) -> pd.DataFrame:
        rows = []
        for inv in invoices:
            for item in inv.items:
                row = {
                    'Date': inv.date.strftime('%Y-%m-%d') if inv.date else '',
                    'Vendor': inv.vendor_name,
                    'Invoice Number': inv.invoice_number or '',
                    'Product': item.product_detail,
                    'Quantity': item.quantity,
                    'Unit Price': item.price,
                    'Amount': item.amount,
                    'Currency': inv.currency,
                    'Original Amount': item.original_amount or '',
                    'Original Currency': item.original_currency or '',
                    'Total Amount': inv.total_amount,
                    'Tax Amount': inv.tax_amount or '',
                    'VAT': item.vat or '',
                    'GST': item.gst or '',
                    'Confidence': inv.confidence_score or '',
                }
                rows.append(row)
        return pd.DataFrame(rows)

    def _invoice_to_dict(self, inv: InvoiceData) -> dict:
        return {
            'date': inv.date.strftime('%Y-%m-%d') if inv.date else '',
            'vendor_name': inv.vendor_name,
            'invoice_number': inv.invoice_number,
            'items': [item.__dict__ for item in inv.items],
            'total_amount': str(inv.total_amount),
            'currency': inv.currency,
            'tax_amount': str(inv.tax_amount) if inv.tax_amount else '',
            'vat': str(inv.vat) if inv.vat else '',
            'gst': str(inv.gst) if inv.gst else '',
            'confidence_score': inv.confidence_score,
        } 