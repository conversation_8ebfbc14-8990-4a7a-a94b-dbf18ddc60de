from pathlib import Path
import shutil
from typing import List
import os

class FileManager:
    def __init__(self, invoice_folder: str, processed_folder: str, backup_folder: str = "./backup"):
        self.invoice_folder = Path(invoice_folder)
        self.processed_folder = Path(processed_folder)
        self.backup_folder = Path(backup_folder)
        self.invoice_folder.mkdir(parents=True, exist_ok=True)
        self.processed_folder.mkdir(parents=True, exist_ok=True)
        self.backup_folder.mkdir(parents=True, exist_ok=True)

    def get_pending_invoices(self) -> List[Path]:
        exts = [".pdf", ".png", ".jpg", ".jpeg", ".tiff"]
        return [f for f in self.invoice_folder.iterdir() if f.suffix.lower() in exts and f.is_file()]

    def move_to_processed(self, invoice_path: Path):
        dest = self.processed_folder / invoice_path.name
        shutil.move(str(invoice_path), str(dest))
        self.backup_invoice(dest)

    def backup_invoice(self, invoice_path: Path):
        backup_dest = self.backup_folder / invoice_path.name
        shutil.copy2(str(invoice_path), str(backup_dest)) 