from enum import Enum
from typing import Optional

class LLMProvider(Enum):
    DEEPSEEK = "deepseek"
    OPENAI = "openai"
    CLAUDE = "claude"
    GEMINI = "gemini"
    MISTRAL = "mistral"
    OPENROUTER = "openrouter"
    CUSTOM = "custom"

class LLMConfig:
    def __init__(self, provider: LLMProvider, api_key: str, base_url: Optional[str] = None, model: Optional[str] = None):
        self.provider = provider
        self.api_key = api_key
        self.base_url = base_url
        self.model = model or self.get_default_model()
    def get_default_model(self) -> str:
        defaults = {
            LLMProvider.DEEPSEEK: "deepseek-chat",
            LLMProvider.OPENAI: "gpt-4-turbo-preview",
            LLMProvider.CLAUDE: "claude-3-sonnet-20240229",
            LLMProvider.GEMINI: "gemini-pro",
            LLMProvider.MISTRAL: "mistral-large-latest",
            LLMProvider.OPENROUTER: "openai/gpt-4-turbo-preview"
        }
        return defaults.get(self.provider, "default-model") 